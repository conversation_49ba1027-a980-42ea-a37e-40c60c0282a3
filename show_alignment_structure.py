"""
Script để hiển thị cấu trúc text alignment trong Google Slides API response
"""

import json

def show_text_alignment_structure():
    """Hiển thị cấu trúc expected của text alignment"""
    
    print("📋 TEXT ALIGNMENT STRUCTURE IN GOOGLE SLIDES API")
    print("=" * 60)
    
    # C<PERSON>u trúc text alignment theo Google Slides API
    alignment_structure = {
        "textAlignment": {
            "alignment": "LEFT | CENTER | RIGHT | JUSTIFIED",
            "lineSpacing": {
                "magnitude": 1.0,
                "unit": "PT"
            },
            "spaceAbove": {
                "magnitude": 0.0,
                "unit": "PT"
            },
            "spaceBelow": {
                "magnitude": 0.0,
                "unit": "PT"
            },
            "indentStart": {
                "magnitude": 0.0,
                "unit": "PT"
            },
            "indentEnd": {
                "magnitude": 0.0,
                "unit": "PT"
            },
            "indentFirstLine": {
                "magnitude": 0.0,
                "unit": "PT"
            },
            "direction": "LEFT_TO_RIGHT | RIGHT_TO_LEFT",
            "lists": {
                "list_id": {
                    "nestingLevel": 0,
                    "bulletStyle": {
                        "type": "BULLET | NUMBER"
                    }
                }
            }
        }
    }
    
    print("📝 Expected JSON Structure:")
    print(json.dumps(alignment_structure, indent=2))
    
    print("\n📋 ALIGNMENT VALUES:")
    print("- LEFT: Căn trái")
    print("- CENTER: Căn giữa") 
    print("- RIGHT: Căn phải")
    print("- JUSTIFIED: Căn đều")
    
    print("\n📏 SPACING & INDENTATION:")
    print("- lineSpacing: Khoảng cách giữa các dòng")
    print("- spaceAbove: Khoảng cách trước đoạn văn")
    print("- spaceBelow: Khoảng cách sau đoạn văn")
    print("- indentStart: Thụt lề từ lề trái")
    print("- indentEnd: Thụt lề từ lề phải")
    print("- indentFirstLine: Thụt lề dòng đầu tiên")
    
    print("\n🔤 DIRECTION:")
    print("- LEFT_TO_RIGHT: Từ trái sang phải")
    print("- RIGHT_TO_LEFT: Từ phải sang trái")
    
    print("\n📋 LISTS:")
    print("- Thông tin về danh sách (bullet points, numbering)")
    print("- nestingLevel: Mức độ lồng nhau")
    print("- bulletStyle: Kiểu bullet/number")

def show_api_response_structure():
    """Hiển thị cấu trúc response từ API"""
    
    print("\n" + "=" * 60)
    print("📊 API RESPONSE STRUCTURE")
    print("=" * 60)
    
    response_structure = {
        "title": "Presentation Title",
        "slide_count": 5,
        "slides": [
            {
                "slide_id": "slide_id_1",
                "elements": [
                    {
                        "type": "shape",
                        "objectId": "element_id_1",
                        "text": "Sample text content",
                        "properties": {
                            "shapeType": "TEXT_BOX",
                            "hasText": True,
                            "shapeStyle": {
                                "backgroundColor": {"red": 1.0, "green": 1.0, "blue": 1.0},
                                "outline": {"weight": {"magnitude": 1.0, "unit": "PT"}}
                            },
                            "textStyle": {
                                "fontFamily": "Arial",
                                "fontSize": {"magnitude": 12.0, "unit": "PT"},
                                "bold": False,
                                "italic": False,
                                "color": {"red": 0.0, "green": 0.0, "blue": 0.0}
                            },
                            "textAlignment": {
                                "alignment": "LEFT",
                                "lineSpacing": {"magnitude": 1.15, "unit": "PT"},
                                "spaceAbove": {"magnitude": 0.0, "unit": "PT"},
                                "spaceBelow": {"magnitude": 0.0, "unit": "PT"}
                            }
                        },
                        "transform": {
                            "translateX": 100.0,
                            "translateY": 200.0,
                            "scaleX": 1.0,
                            "scaleY": 1.0
                        }
                    }
                ]
            }
        ]
    }
    
    print("📝 API Response Structure:")
    print(json.dumps(response_structure, indent=2))

if __name__ == "__main__":
    show_text_alignment_structure()
    show_api_response_structure()
    
    print("\n" + "=" * 60)
    print("💡 USAGE TIPS:")
    print("=" * 60)
    print("1. Chạy server FastAPI trước")
    print("2. Đảm bảo Google Slides service hoạt động")
    print("3. Sử dụng test_alignment_simple.py để test")
    print("4. Kiểm tra file alignment_test_response.json để xem kết quả")
    print("=" * 60)
