"""
Test script để kiểm tra thông tin text alignment từ Google Slides API
"""

import requests
import json
import sys

# Configuration
API_BASE_URL = "http://localhost:8000/api/v1/slides"
TEST_PRESENTATION_ID = "1EnhbJcoz6YZ3LbyJ6-ZuM8oRGdDBfVkNaLc4CNFAxvE"  # Thay bằng ID thực tế

def print_text_alignment_info(element, element_index):
    """In thông tin text alignment chi tiết của một element"""
    
    print(f"\n    📋 Element {element_index + 1} Text Alignment Details:")
    print(f"      Type: {element.get('type', 'N/A')}")
    print(f"      Object ID: {element.get('objectId', 'N/A')}")
    
    # Text content
    text = element.get('text', '')
    if text:
        text_preview = text[:100] + "..." if len(text) > 100 else text
        print(f"      Text: {text_preview}")
    
    # Properties and styles
    properties = element.get('properties', {})
    
    # Text alignment information
    if element.get('type') == 'shape' and properties.get('hasText'):
        text_alignment = properties.get('textAlignment', {})
        if text_alignment:
            print(f"      📐 Text Alignment:")
            
            # Basic alignment
            if 'alignment' in text_alignment:
                alignment = text_alignment['alignment']
                print(f"        Alignment: {alignment}")
            
            # Line spacing
            if 'lineSpacing' in text_alignment:
                line_spacing = text_alignment['lineSpacing']
                if isinstance(line_spacing, dict):
                    if 'magnitude' in line_spacing:
                        print(f"        Line Spacing: {line_spacing.get('magnitude', 'N/A')} {line_spacing.get('unit', '')}")
                    elif 'percentage' in line_spacing:
                        print(f"        Line Spacing: {line_spacing.get('percentage', 'N/A')}%")
                else:
                    print(f"        Line Spacing: {line_spacing}")
            
            # Space above/below
            if 'spaceAbove' in text_alignment:
                space_above = text_alignment['spaceAbove']
                if isinstance(space_above, dict) and 'magnitude' in space_above:
                    print(f"        Space Above: {space_above.get('magnitude', 'N/A')} {space_above.get('unit', '')}")
                else:
                    print(f"        Space Above: {space_above}")
            
            if 'spaceBelow' in text_alignment:
                space_below = text_alignment['spaceBelow']
                if isinstance(space_below, dict) and 'magnitude' in space_below:
                    print(f"        Space Below: {space_below.get('magnitude', 'N/A')} {space_below.get('unit', '')}")
                else:
                    print(f"        Space Below: {space_below}")
            
            # Indentation
            if 'indentStart' in text_alignment:
                indent_start = text_alignment['indentStart']
                if isinstance(indent_start, dict) and 'magnitude' in indent_start:
                    print(f"        Indent Start: {indent_start.get('magnitude', 'N/A')} {indent_start.get('unit', '')}")
                else:
                    print(f"        Indent Start: {indent_start}")
            
            if 'indentEnd' in text_alignment:
                indent_end = text_alignment['indentEnd']
                if isinstance(indent_end, dict) and 'magnitude' in indent_end:
                    print(f"        Indent End: {indent_end.get('magnitude', 'N/A')} {indent_end.get('unit', '')}")
                else:
                    print(f"        Indent End: {indent_end}")
            
            if 'indentFirstLine' in text_alignment:
                indent_first = text_alignment['indentFirstLine']
                if isinstance(indent_first, dict) and 'magnitude' in indent_first:
                    print(f"        First Line Indent: {indent_first.get('magnitude', 'N/A')} {indent_first.get('unit', '')}")
                else:
                    print(f"        First Line Indent: {indent_first}")
            
            # Direction
            if 'direction' in text_alignment:
                print(f"        Direction: {text_alignment['direction']}")
            
            # Lists
            if 'lists' in text_alignment:
                lists_info = text_alignment['lists']
                print(f"        Lists: {len(lists_info)} list(s) found")
                for list_id, list_info in lists_info.items():
                    print(f"          List {list_id}: {list_info}")
        
        # Text style information
        text_style = properties.get('textStyle', {})
        if text_style:
            print(f"      📝 Text Style:")
            if 'fontFamily' in text_style:
                print(f"        Font Family: {text_style['fontFamily']}")
            if 'fontSize' in text_style:
                font_size = text_style['fontSize']
                if isinstance(font_size, dict):
                    print(f"        Font Size: {font_size.get('magnitude', 'N/A')} {font_size.get('unit', '')}")
                else:
                    print(f"        Font Size: {font_size}")
            if 'bold' in text_style:
                print(f"        Bold: {text_style['bold']}")
            if 'italic' in text_style:
                print(f"        Italic: {text_style['italic']}")
            if 'underline' in text_style:
                print(f"        Underline: {text_style['underline']}")
            if 'color' in text_style:
                color = text_style['color']
                if 'red' in color:
                    print(f"        Text Color: RGB({color['red']:.3f}, {color['green']:.3f}, {color['blue']:.3f})")
                elif 'themeColor' in color:
                    print(f"        Text Color: Theme - {color['themeColor']}")

def test_text_alignment():
    """Test API và hiển thị thông tin text alignment"""
    
    print("🧪 Testing Text Alignment Information...")
    print(f"📋 API URL: {API_BASE_URL}/slide-info/{TEST_PRESENTATION_ID}")
    
    try:
        # Gọi API
        response = requests.get(f"{API_BASE_URL}/slide-info/{TEST_PRESENTATION_ID}")
        
        print(f"📊 Response Status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print("✅ API call successful!")
            
            # In thông tin cơ bản
            print(f"\n📝 Presentation Info:")
            print(f"  Title: {data.get('title', 'N/A')}")
            print(f"  Slide Count: {data.get('slide_count', 0)}")
            
            # In thông tin chi tiết về text alignment
            slides = data.get('slides', [])
            print(f"\n📋 Text Alignment Information:")
            
            for i, slide in enumerate(slides[:3]):  # Chỉ hiển thị 3 slide đầu
                print(f"\n  🎯 Slide {i+1}:")
                print(f"    ID: {slide.get('slide_id', 'N/A')}")
                print(f"    Elements: {len(slide.get('elements', []))}")
                
                # In thông tin chi tiết về text alignment của từng element
                elements = slide.get('elements', [])
                text_elements = [elem for elem in elements if elem.get('type') == 'shape' and elem.get('text')]
                
                if text_elements:
                    print(f"    Text Elements: {len(text_elements)}")
                    for j, element in enumerate(text_elements[:2]):  # Chỉ hiển thị 2 text element đầu
                        print_text_alignment_info(element, j)
                else:
                    print(f"    No text elements found in this slide")
            
            if len(slides) > 3:
                print(f"\n  ... and {len(slides) - 3} more slides")
            
            # Lưu full response vào file để xem chi tiết
            with open('text_alignment_response.json', 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
            print(f"\n💾 Full response saved to: text_alignment_response.json")
            
        else:
            print("❌ API call failed!")
            print(f"Error: {response.text}")
            
    except requests.exceptions.ConnectionError:
        print("❌ Connection error! Make sure the FastAPI server is running on localhost:8000")
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    print("=" * 80)
    print("🚀 TEXT ALIGNMENT API TEST")
    print("=" * 80)
    
    # Kiểm tra tham số dòng lệnh
    if len(sys.argv) > 1:
        TEST_PRESENTATION_ID = sys.argv[1]
        print(f"📋 Using custom presentation ID: {TEST_PRESENTATION_ID}")
    else:
        print(f"📋 Using default presentation ID: {TEST_PRESENTATION_ID}")
        print("💡 Usage: python test_slide_alignment.py <presentation_id>")
    
    print()
    
    # Chạy test
    test_text_alignment()
    
    print("\n" + "=" * 80)
    print("🏁 TEXT ALIGNMENT TEST COMPLETED")
    print("=" * 80)
