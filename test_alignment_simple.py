"""
Simple test script để kiểm tra text alignment khi service hoạt động
"""

import requests
import json

# Configuration
API_BASE_URL = "http://localhost:8000/api/v1/slides"
TEST_PRESENTATION_ID = "1EnhbJcoz6YZ3LbyJ6-ZuM8oRGdDBfVkNaLc4CNFAxvE"

def test_health():
    """Test health check"""
    try:
        response = requests.get(f"{API_BASE_URL}/health")
        print(f"Health Status: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"Service Status: {data.get('status')}")
            services = data.get('services', {})
            for service, status in services.items():
                status_icon = "✅" if status else "❌"
                print(f"  {status_icon} {service}: {status}")
            return data.get('status') == 'healthy'
        return False
    except Exception as e:
        print(f"Health check error: {e}")
        return False

def test_alignment_when_ready():
    """Test alignment khi service sẵn sàng"""
    print("🧪 Testing Text Alignment...")
    
    if not test_health():
        print("❌ Service not healthy. Please start the service first.")
        return
    
    try:
        response = requests.get(f"{API_BASE_URL}/slide-info/{TEST_PRESENTATION_ID}")
        print(f"API Response Status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print("✅ API call successful!")
            
            # Tìm text elements và hiển thị alignment info
            slides = data.get('slides', [])
            alignment_found = False
            
            for i, slide in enumerate(slides[:2]):
                elements = slide.get('elements', [])
                for j, element in enumerate(elements):
                    if element.get('type') == 'shape' and element.get('text'):
                        properties = element.get('properties', {})
                        text_alignment = properties.get('textAlignment', {})
                        
                        if text_alignment:
                            alignment_found = True
                            print(f"\n📋 Slide {i+1}, Element {j+1}:")
                            print(f"  Text: {element.get('text', '')[:50]}...")
                            print(f"  Alignment Info: {text_alignment}")
            
            if not alignment_found:
                print("ℹ️ No text alignment information found in the first 2 slides")
                print("This might be normal if the slides don't have paragraph formatting")
            
            # Save response for inspection
            with open('alignment_test_response.json', 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
            print(f"\n💾 Response saved to: alignment_test_response.json")
            
        else:
            print(f"❌ API call failed: {response.text}")
            
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    print("=" * 60)
    print("🚀 SIMPLE TEXT ALIGNMENT TEST")
    print("=" * 60)
    test_alignment_when_ready()
    print("=" * 60)
